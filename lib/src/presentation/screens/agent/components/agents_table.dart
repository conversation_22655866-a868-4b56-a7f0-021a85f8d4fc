import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../../core/config/responsive.dart';
import '/src/presentation/shared/components/tables/action_button_eye.dart';
import '/src/core/config/app_strings.dart';
import '/src/core/config/json_consts.dart';
import '/src/core/config/constants.dart';
import '/src/core/theme/app_fonts.dart';
import '/src/core/theme/app_theme.dart';
import '/src/domain/models/agent.dart';

import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';

class AgentsTable extends HookWidget {
  final bool showEditOptions;
  final bool showRecruits;
  const AgentsTable({
    super.key,
    this.showEditOptions = false,
    this.showRecruits = false,
  });

  // Method to read sampleagentDataResponse and convert to agent model
  // Method to read sampleagentDataResponse and convert to agent model
  List<Agent> readagentDataFromJson() {
    try {
      // Debug: Print the JSON string length and first few characters

      // Parse the JSON string
      final Map<String, dynamic> jsonData = json.decode(
        sampleAgentDataResponse,
      );

      // Extract agentData array
      final List<dynamic> agentDataList = jsonData[agentDataKey] ?? [];

      // Convert each JSON object to Agent model
      return agentDataList
          .map((json) => Agent.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error parsing agent data: $e');
      return <Agent>[];
    }
  }

  @override
  Widget build(BuildContext context) {
    // Call the method to read and print agent data from JSON
    final List<Agent> agentData = readagentDataFromJson();

    // Use predefined headers from app_strings.dart
    final List<String> formattedHeaders = showRecruits
        ? [
            agentName,
            agentLevel,
            agentJoinDate,
            agentRefferedBy,
            agentTotalSales,
            agentCommission,
          ]
        : [
            agentName,
            agentLevel,
            agentJoinDate,
            agentSoldHomes,
            agentEarning,
            agentState,
            agentCity,
          ];

    final sortedAgents = useState<List<Agent>>(agentData);

    void handleSort(String columnName, bool ascending) {
      final sorted = List<Agent>.from(sortedAgents.value);
      sorted.sort((a, b) {
        dynamic aValue, bValue;

        switch (columnName) {
          case agentName:
            aValue = a.name;
            bValue = b.name;
            break;
          case agentContact:
            aValue = a.contact;
            bValue = b.contact;
            break;
          case agentEmail:
            aValue = a.email;
            bValue = b.email;
            break;
          case agentJoinDate:
            aValue = a.joinDate;
            bValue = b.joinDate;
            break;
          case agentState:
            aValue = a.state;
            bValue = b.state;
            break;
          case agentCity:
            aValue = a.city;
            bValue = b.city;
            break;
          case agentLevel:
            aValue = a.level;
            bValue = b.level;
            break;
          case agentTotalDeals:
            aValue = a.totalDeals;
            bValue = b.totalDeals;
            break;
          case agentEarning:
            aValue = a.earning;
            bValue = b.earning;
            break;
          case agentStatus:
            aValue = a.status;
            bValue = b.status;
            break;
          default:
            aValue = '';
            bValue = '';
        }

        final comparison = aValue is num && bValue is num
            ? aValue.compareTo(bValue)
            : aValue is DateTime && bValue is DateTime
            ? aValue.compareTo(bValue)
            : aValue.toString().compareTo(bValue.toString());
        return ascending ? comparison : -comparison;
      });
      sortedAgents.value = sorted;
    }

    final Size size = MediaQuery.of(context).size;
    final isDesktop = Responsive.isDesktop(context);

    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //Customized table layout
            CustomDataTableWidget<Agent>(
              data: sortedAgents.value,
              title: agents,
              titleIcon: "$iconAssetpath/user.png",
              searchHint: searchAgent,
              searchFn: (agent) =>
                  agent.name +
                  agent.contact +
                  agent.email +
                  agent.state +
                  agent.city +
                  agent.level +
                  agent.totalDeals.toString() +
                  agent.earning.toString(),
              // Dynamic filtering system
              filterColumnNames: [
                agentName, // name
                agentState, // state
                agentLevel, // level
              ],
              filterValueExtractors: {
                agentName: (agent) => agent.name,
                agentState: (agent) => agent.state,
                agentLevel: (agent) => agent.level,
              },
              columnNames: formattedHeaders,
              cellBuilders: showRecruits
                  ? [
                      (agent) => agent.name,
                      (agent) => agent.level,
                      (agent) =>
                          '${agent.joinDate.day}/${agent.joinDate.month}/${agent.joinDate.year}',
                      (agent) => agent.relatedBroker,
                      (agent) => agent.totalSales.toString(),
                      (agent) => '₹${agent.commission.toStringAsFixed(2)}',
                    ]
                  : [
                      (agent) => agent.name,
                      (agent) => agent.level,
                      (agent) =>
                          '${agent.joinDate.day}/${agent.joinDate.month}/${agent.joinDate.year}',
                      (agent) => agent.soldHomes.toString(),
                      (agent) => '₹${agent.earning.toStringAsFixed(2)}',
                      (agent) => agent.state,
                      (agent) => agent.city,
                    ],

              /// to show icons before the cell content. eg: username and user icon
              iconCellBuilders: [
                (agent) => TableCellData(
                  text: agent.name,
                  leftIconAsset: "$iconAssetpath/agent_round.png",
                  iconSize: 28,
                ),
                null, // contact - no icon
                null,
                showRecruits
                    ? (agent) => TableCellData(
                        text: agent.relatedBroker,
                        leftIconAsset: "$iconAssetpath/agent_round.png",
                        iconSize: 28,
                      )
                    : null, // reffered by broker name
                // address - no icon
                null, // joinDate - no icon
                null, // agents - no icon
                null, // totalSales - no icon
              ],

              /// Boolean flags to indicate which columns use icon cell builders. can enable/disable by setting this flag
              useIconBuilders: [
                true, // name - use icon
                false, // contact - use text
                false, // address - use text
                showRecruits, // reffered by broker name - use icon
                false, // joinDate - use text
                false, // agents - use text
                false, // totalSales - use text
              ],

              /// Widget builders for styled cells like active/inactive
              widgetCellBuilders: [
                null, // name - use text
                null, // contact - use text
                null, // email - use text
                null, // joinDate - use text
                null, // state - use text
                null, // city - use text
                null, // level - use text
                null, // totalDeals - use text
                null, // earning - use text
                (context, agent) => Container(
                  // status - widget that fills cell width but maintains its own height
                  width: double.infinity, // Fill the entire cell width
                  // Small margin from cell edges
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: agent.status
                        ? AppTheme
                              .agentStatusActiveBg // Light green background
                        : AppTheme
                              .agentStatusInactiveBg, // Light red/pink background
                    borderRadius: BorderRadius.circular(
                      20,
                    ), // More rounded for oval shape
                  ),
                  child: Text(
                    agent.status ? active : inactive,
                    style: AppFonts.semiBoldTextStyle(
                      12,
                      color: agent.status
                          ? AppTheme.agentStatusActiveText
                          : AppTheme.agentStatusInactiveText,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
              // Boolean flags to indicate which columns use widget builders
              useWidgetBuilders: [
                false, // name - use text
                false, // contact - use text
                false, // email - use text
                false, // joinDate - use text
                false, // state - use text
                false, // city - use text
                false, // level - use text
                false, // totalDeals - use text
                false, // earning - use text
                true, // status - use widget
              ],
              actionBuilders: [
                (context, agent) => ActionButtonEye(
                  onPressed: () => _onAgentAction(context, agent),
                  isCompact: true,
                  isMobile: false,
                ),
                if (showEditOptions) ...[
                  (context, agent) => ActionButtonEye(
                    onPressed: () => _onAgentAction(context, agent),
                    isCompact: true,
                    isMobile: false,
                    padding: 8,
                    icon: '$iconAssetpath/table_edit.png',
                  ),
                  (context, agent) => ActionButtonEye(
                    onPressed: () => _onAgentAction(context, agent),
                    isCompact: true,
                    isMobile: false,
                    padding: 8,
                    icon: '$iconAssetpath/delete.png',
                  ),
                ],
              ],
              mobileCardBuilder: (context, agent) =>
                  _buildMobileAgentCard(agent, context),
              onSort: handleSort,
              emptyStateMessage: noDataAvailable,
              useMinHeight: isDesktop,
              minHeight: constraints.maxHeight,
            ),
          ],
        );
      },
    );
  }

  void _onAgentAction(BuildContext context, Agent agent) {
    // Navigate to agent detail or show action
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Action clicked for ${agent.name}')));
  }

  Widget _buildMobileAgentCard(Agent agent, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(agent.name, style: TextStyle(fontWeight: FontWeight.bold)),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: agent.status
                      ? AppTheme
                            .agentStatusActiveBg // Light green background
                      : AppTheme
                            .agentStatusInactiveBg, // Light red/pink background
                  borderRadius: BorderRadius.circular(
                    20,
                  ), // More rounded for oval shape
                ),
                child: Text(
                  agent.status ? active : inactive,
                  style: AppFonts.normalTextStyle(
                    12,
                    color: agent.status
                        ? AppTheme
                              .agentStatusActiveText // Darker green text
                        : AppTheme.agentStatusInactiveText,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text('$agentContact: ${agent.contact}'),
          Text('$agentEmail: ${agent.email}'),
          Text('$agentState: ${agent.state}'),
          Text('$agentCity: ${agent.city}'),
          Text('$agentLevel: ${agent.level}'),
          Text('$agentTotalDeals: ${agent.totalDeals}'),
          Text('$agentEarning: ₹${agent.earning.toStringAsFixed(2)}'),
          Text(
            '$agentJoinDate: ${agent.joinDate.day}/${agent.joinDate.month}/${agent.joinDate.year}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onAgentAction(context, agent),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }
}
