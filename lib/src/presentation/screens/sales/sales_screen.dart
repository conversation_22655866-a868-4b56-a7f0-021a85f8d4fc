import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import '../../../core/theme/app_theme.dart';
import '../../shared/components/tables/action_button_eye.dart';
import '/src/core/config/json_consts.dart';
import '/src/domain/models/sales.dart';

import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/config/constants.dart';

class SalesScreen extends HookWidget {
  final Function()? handleSaleSelection;
  const SalesScreen({Key? key, this.handleSaleSelection}) : super(key: key);

  // Method to read sampleSalesDataResponse and convert to Sales model
  List<Sales> readSalesDataFromJson() {
    try {
      // Parse the JSON string
      final Map<String, dynamic> jsonData = json.decode(
        sampleSalesDataResponse,
      );

      // Extract salesData array
      final List<dynamic> salesDataList = jsonData[salesDataKey] ?? [];
      return salesDataList
          .map((json) => Sales.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error parsing sales data: $e');
      return <Sales>[];
    }
  }

  @override
  Widget build(BuildContext context) {
    // Call the method to read and print sales data from JSON
    final List<Sales> salesData = readSalesDataFromJson();

    // Use predefined headers from app_strings.dart
    final List<String> formattedHeaders = [
      salesTransactionIdColumnHeader,
      salesAgentColumnHeader,
      salesPropertyTypeColumnHeader,
      salesPropertyAddressColumnHeader,
      salesPropertyValueColumnHeader,
      salesBuyerNameColumnHeader,
      salesBuyerAddressColumnHeader,
      salesListingDateColumnHeader,
      salesDateColumnHeader,
      salesAmountColumnHeader,
      salesCommissionColumnHeader,
      salesCommissionAmtColumnHeader,
    ];

    final sortedSales = useState<List<Sales>>(salesData);

    void handleSort(String columnName, bool ascending) {
      final sorted = List<Sales>.from(sortedSales.value);
      sorted.sort((a, b) {
        dynamic aValue, bValue;

        switch (columnName) {
          case salesTransactionIdColumnHeader:
            aValue = a.transactionId;
            bValue = b.transactionId;
            break;
          case salesAgentColumnHeader:
            aValue = a.agentName;
            bValue = b.agentName;
            break;
          case salesPropertyTypeColumnHeader:
            aValue = a.propertyType;
            bValue = b.propertyType;
            break;
          case salesPropertyAddressColumnHeader:
            aValue = a.propertyAddress;
            bValue = b.propertyAddress;
            break;
          case salesPropertyValueColumnHeader:
            aValue = a.propertyValue;
            bValue = b.propertyValue;
            break;
          case salesBuyerNameColumnHeader:
            aValue = a.buyerName;
            bValue = b.buyerName;
            break;
          case salesBuyerAddressColumnHeader:
            aValue = a.buyerAddress;
            bValue = b.buyerAddress;
            break;
          case salesListingDateColumnHeader:
            aValue = a.listingDate;
            bValue = b.listingDate;
            break;
          case salesDateColumnHeader:
            aValue = a.saleDate;
            bValue = b.saleDate;
            break;
          case salesAmountColumnHeader:
            aValue = a.salePrice;
            bValue = b.salePrice;
            break;
          case salesCommissionColumnHeader:
            aValue = a.commission;
            bValue = b.commission;
            break;
          case salesCommissionAmtColumnHeader:
            aValue = a.commissionAmt;
            bValue = b.commissionAmt;
            break;
          default:
            aValue = '';
            bValue = '';
        }

        final comparison = aValue is num && bValue is num
            ? aValue.compareTo(bValue)
            : aValue is DateTime && bValue is DateTime
            ? aValue.compareTo(bValue)
            : aValue.toString().compareTo(bValue.toString());
        return ascending ? comparison : -comparison;
      });
      sortedSales.value = sorted;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Customized table layout
        CustomDataTableWidget<Sales>(
          data: sortedSales.value,
          title: salesTab,
          titleIcon: "$iconAssetpath/user.png",
          searchHint: searchHint,
          searchFn: (sale) =>
              sale.transactionId +
              sale.agentName +
              sale.propertyType +
              sale.propertyAddress +
              sale.buyerName +
              sale.buyerAddress +
              sale.listingDate.toString() +
              sale.saleDate.toString() +
              sale.salePrice.toString() +
              sale.commission.toString() +
              sale.commissionAmt.toString(),
          // Dynamic filtering system
          filterColumnNames: [
            salesPropertyTypeColumnHeader, // propertyType
            salesAgentColumnHeader, // agentName
            salesTransactionIdColumnHeader, // transactionId
          ],
          filterValueExtractors: {
            salesPropertyTypeColumnHeader: (sale) => sale.propertyType,
            salesAgentColumnHeader: (sale) => sale.agentName,
            salesTransactionIdColumnHeader: (sale) => sale.transactionId,
          },
          columnNames: formattedHeaders,
          cellBuilders: [
            (sale) => sale.transactionId,
            (sale) => sale.agentName,
            (sale) => sale.propertyType,
            (sale) => sale.propertyAddress,
            (sale) => '₹${sale.propertyValue.toStringAsFixed(2)}',
            (sale) => sale.buyerName,
            (sale) => sale.buyerAddress,
            (sale) =>
                '${sale.listingDate.day}/${sale.listingDate.month}/${sale.listingDate.year}',
            (sale) =>
                '${sale.saleDate.day}/${sale.saleDate.month}/${sale.saleDate.year}',
            (sale) => '₹${sale.salePrice.toStringAsFixed(2)}',
            (sale) => '${sale.commission.toStringAsFixed(1)}%',
            (sale) => '₹${sale.commissionAmt.toStringAsFixed(2)}',
          ],
          // Widget builders for styled cells
          widgetCellBuilders: [
            null, // transactionId - use text
            null, // agentName - use text
            (context, sale) => Container(
              // propertyType - use widget
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: sale.propertyType == "Land"
                    ? Colors.green.withValues(alpha: 0.1)
                    : Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                sale.propertyType,
                style: AppFonts.mediumTextStyle(
                  12,
                  color: sale.propertyType == "Land"
                      ? Colors.green
                      : Colors.blue,
                ),
              ),
            ),
            null, // propertyAddress - use text
            null, // propertyValue - use text
            null, // buyerName - use text
            null, // buyerAddress - use text
            null, // listingDate - use text
            null, // saleDate - use text
            null, // salePrice - use text
            null, // commission - use text
            null, // commissionAmt - use text
          ],
          // Boolean flags to indicate which columns use widget builders
          useWidgetBuilders: [
            false, // transactionId
            false, // agentName
            false, // propertyType - use widget builder
            false, // propertyAddress
            false, // propertyValue
            false, // buyerName
            false, // buyerAddress
            false, // listingDate
            false, // saleDate
            false, // salePrice
            false, // commission
            false, // commissionAmt
          ],
          actionBuilders: [
            (context, sale) => ActionButtonEye(
              onPressed: () => _onSaleAction(context, sale),
              isCompact: true,
              isMobile: false,
            ),
          ],

          mobileCardBuilder: (context, sale) =>
              _buildMobileSaleCard(sale, context),
          onSort: handleSort,
          emptyStateMessage: noDataAvailable,
        ),
      ],
    );
  }

  void _onSaleAction(BuildContext context, Sales sale) {
    // Navigate to sales detail or show action
    // ScaffoldMessenger.of(context).showSnackBar(
    //   SnackBar(content: Text('Action clicked for ${sale.transactionId}')),
    // );
    handleSaleSelection?.call();
  }

  Widget _buildMobileSaleCard(Sales sale, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                sale.transactionId,
                style: AppFonts.semiBoldTextStyle(
                  14,
                  color: AppTheme.primaryTextColor,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: sale.propertyType == "Lease"
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  sale.propertyType,
                  style: AppFonts.mediumTextStyle(
                    12,
                    color: sale.propertyType == "Lease"
                        ? Colors.green
                        : Colors.blue,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text('$salesAgentColumnHeader: ${sale.agentName}'),
          Text('$salesPropertyAddressColumnHeader: ${sale.propertyAddress}'),
          Text('$salesBuyerNameColumnHeader: ${sale.buyerName}'),
          Text(
            '$salesListingDateColumnHeader: ${sale.listingDate.day}/${sale.listingDate.month}/${sale.listingDate.year}',
          ),
          Text(
            '$salesDateColumnHeader: ${sale.saleDate.day}/${sale.saleDate.month}/${sale.saleDate.year}',
          ),
          Text('$salesBuyerAddressColumnHeader: ${sale.buyerAddress}'),
          Text(
            '$salesAmountColumnHeader: ₹${sale.salePrice.toStringAsFixed(2)}',
          ),
          Text(
            '$salesCommissionColumnHeader: ₹${sale.commission.toStringAsFixed(2)}',
          ),
          Text(
            '$salesCommissionAmtColumnHeader: ₹${sale.commissionAmt.toStringAsFixed(2)}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onSaleAction(context, sale),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }
}
