part of 'top_performers_cubit.dart';

abstract class TopPerformersState {
  final List<TopPerformers>? topPerformers;
  TopPerformersState({this.topPerformers});
}

final class TopPerformersInitial extends TopPerformersState {}

final class TopPerformersLoading extends TopPerformersState {}

final class TopPerformersLoaded extends TopPerformersState {
  TopPerformersLoaded({super.topPerformers});

  @override
  List<Object?> get props => [topPerformers];
}

final class TopPerformersError extends TopPerformersState {
  final String message;
  final int? statusCode;

  TopPerformersError({required this.message, this.statusCode});

  @override
  List<Object?> get props => [message, statusCode];
}
