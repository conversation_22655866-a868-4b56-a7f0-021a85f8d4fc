class TopPerformers {
  String id;
  String name;
  int downlineAgentsCount;
  int monthlyRevenue;
  int monthlySalesCount;
  String associatedBroker;
  int rank;

  TopPerformers({
    required this.id,
    required this.name,
    required this.downlineAgentsCount,
    required this.monthlyRevenue,
    required this.monthlySalesCount,
    required this.associatedBroker,
    required this.rank,
  });

  TopPerformers copyWith({
    String? id,
    String? name,
    int? downlineAgentsCount,
    int? monthlyRevenue,
    int? monthlySalesCount,
    String? associatedBroker,
    int? rank,
  }) => TopPerformers(
    id: id ?? this.id,
    name: name ?? this.name,
    downlineAgentsCount: downlineAgentsCount ?? this.downlineAgentsCount,
    monthlyRevenue: monthlyRevenue ?? this.monthlyRevenue,
    monthlySalesCount: monthlySalesCount ?? this.monthlySalesCount,
    associatedBroker: associatedBroker ?? this.associatedBroker,
    rank: rank ?? this.rank,
  );
}
