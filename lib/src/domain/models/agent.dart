import 'dart:ui';

import 'package:flutter/material.dart';

import '../../core/utils/helper.dart';

class Agent {
  String name;
  int sales;
  double amount;
  double commission;
  String contact;
  String email;
  List<Agent> agents;
  Color color;
  String imageUrl;
  DateTime joinDate;
  String state;
  String city;
  String level;
  int totalDeals;
  double earning;
  bool status;
  String relatedBroker;
  int totalAgents;
  int soldHomes;
  int totalSales;

  Agent({
    required this.name,
    required this.sales,
    required this.amount,
    required this.commission,
    required this.contact,
    required this.email,
    required this.agents,
    required this.color,
    required this.imageUrl,
    required this.joinDate,
    required this.state,
    required this.city,
    required this.level,
    required this.totalDeals,
    required this.earning,
    required this.status,
    this.relatedBroker = '',
    this.totalAgents = 0,
    this.soldHomes = 0,
    this.totalSales = 0,
  });

  factory Agent.fromJson(Map<String, dynamic> json) {
    return Agent(
      name: json['name']?.toString() ?? '',
      sales: toInt(json['sales']),
      amount: toDouble(json['amount']),
      commission: toDouble(json['commission']),
      contact: json['contact']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
      agents: _toAgentList(json['agents']),
      totalAgents: toInt(json['totalAgents']),
      color: json['color'] as Color? ?? Colors.blue,
      imageUrl: json['imageUrl']?.toString() ?? '',
      joinDate: parseDate(json['joinDate']),
      state: json['state']?.toString() ?? '',
      city: json['city']?.toString() ?? '',
      level: json['level']?.toString() ?? '',
      totalDeals: toInt(json['totalDeals']),
      earning: toDouble(json['earning']),
      status: json['status'] as bool? ?? true,
      relatedBroker: json['relatedBroker']?.toString() ?? '',
      soldHomes: toInt(json['soldHomes']),
      totalSales: toInt(json['totalSales']),
    );
  }

  // Helper method for safe Agent list conversion
  static List<Agent> _toAgentList(dynamic value) {
    if (value == null) return <Agent>[];
    if (value is List) {
      try {
        return value
            .map((item) => Agent.fromJson(item as Map<String, dynamic>))
            .toList();
      } catch (e) {
        return <Agent>[];
      }
    }
    return <Agent>[];
  }
}
